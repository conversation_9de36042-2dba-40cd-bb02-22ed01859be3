{"name": "MiniBalance", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "USER", "files": [{"path": "stm32f10x_it.c"}, {"path": "system_stm32f10x.c"}, {"path": "MiniBalance.c"}], "folders": []}, {"name": "CORE", "files": [{"path": "../MiniBalance_COER/core_cm3.c"}, {"path": "../MiniBalance_COER/startup_stm32f10x_md.s"}], "folders": []}, {"name": "FWLIB", "files": [{"path": "../STM32F10x_FWLib/src/misc.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_adc.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_bkp.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_can.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_cec.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_crc.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_dac.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_dbgmcu.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_dma.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_exti.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_flash.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_fsmc.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_gpio.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_i2c.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_iwdg.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_pwr.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_rcc.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_rtc.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_sdio.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_spi.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_tim.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_usart.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_wwdg.c"}], "folders": []}, {"name": "SYSTEM", "files": [{"path": "../SYSTEM/delay/delay.c"}, {"path": "../SYSTEM/sys/sys.c"}, {"path": "../SYSTEM/usart/usart.c"}], "folders": []}, {"name": "HAREWARE", "files": [{"path": "../MiniBalance_HARDWARE/led.c"}, {"path": "../MiniBalance_HARDWARE/oled.c"}, {"path": "../MiniBalance_HARDWARE/servo.c"}, {"path": "../MiniBalance_HARDWARE/timer.c"}, {"path": "../MiniBalance_HARDWARE/adc.c"}, {"path": "../MiniBalance_HARDWARE/key.c"}], "folders": []}, {"name": "MiniBalance", "files": [{"path": "../MiniBalance/CONTROL/control.c"}, {"path": "../MiniBalance/show/show.c"}], "folders": []}]}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "7891fa34fff5ece579aba2bed662e438"}, "targets": {"MiniBalance": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M3", "archExtensions": "", "floatingPointHardware": "none", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x5000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x10000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": [".", "../MiniBalance_COER", "../STM32F10x_FWLib/inc", "../MiniBalance_HARDWARE", "../SYSTEM", "../SYSTEM/delay", "../SYSTEM/sys", "../SYSTEM/usart", "../MiniBalance_HARDWARE/Inc", "../MiniBalance/CONTROL", ".cmsis/include", "RTE/_MiniBalance"], "libList": [], "defineList": ["STM32F10X_", "HD", "USE_STDPERIPH_DRIVER"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "[Copy linker output for Keil User Commands]", "command": "mkdir ${KEIL_OUTPUT_DIR} & copy \"${OutDir}\\${ProjectName}.axf\" \"${KEIL_OUTPUT_DIR}\\${ProjectName}.axf\"", "disable": false, "abortAfterFailed": true}, {"name": "fromelf --bin --output=..\\WHEELTEC.bin ..\\OBJ\\Minibalance.axf", "command": "fromelf --bin --output=..\\WHEELTEC.bin ..\\OBJ\\Minibalance.axf", "disable": false, "abortAfterFailed": true}], "global": {"use-microLIB": false, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "unspecified"}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "xo-base": "", "ro-base": "0x08000000", "rw-base": "0x20000000"}}}}}, "version": "3.6"}