# STM32F103C8T6 舵机控制项目 Makefile
# 适用于ARM GCC工具链

# 项目名称
PROJECT = ServoControl

# 目标芯片
MCU = cortex-m3

# 源文件
SOURCES = main.c \
          sys.c \
          delay.c \
          led.c \
          key.c \
          servo.c \
          stm32f10x_it.c

# 包含路径
INCLUDES = -I. \
           -I./STM32F10x_StdPeriph_Driver/inc \
           -I./CMSIS

# 编译器设置
CC = arm-none-eabi-gcc
OBJCOPY = arm-none-eabi-objcopy
SIZE = arm-none-eabi-size

# 编译选项
CFLAGS = -mcpu=$(MCU) \
         -mthumb \
         -Wall \
         -fdata-sections \
         -ffunction-sections \
         -g \
         -O2 \
         -DSTM32F10X_MD \
         -DUSE_STDPERIPH_DRIVER

# 链接选项
LDFLAGS = -mcpu=$(MCU) \
          -mthumb \
          -specs=nano.specs \
          -T stm32f103c8t6.ld \
          -Wl,--gc-sections \
          -Wl,-Map=$(PROJECT).map

# 目标文件
OBJECTS = $(SOURCES:.c=.o)

# 默认目标
all: $(PROJECT).hex

# 编译规则
%.o: %.c
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 链接规则
$(PROJECT).elf: $(OBJECTS)
	$(CC) $(LDFLAGS) $(OBJECTS) -o $@

# 生成HEX文件
$(PROJECT).hex: $(PROJECT).elf
	$(OBJCOPY) -O ihex $< $@
	$(SIZE) $<

# 清理
clean:
	rm -f $(OBJECTS) $(PROJECT).elf $(PROJECT).hex $(PROJECT).map

# 烧录 (使用ST-Link)
flash: $(PROJECT).hex
	st-flash --format ihex write $(PROJECT).hex

.PHONY: all clean flash
