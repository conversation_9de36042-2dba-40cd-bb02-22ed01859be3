Dependencies for Project 'ServoControl', Target 'ServoControl': (DO NOT MODIFY !)
CompilerVersion: 6230000::V6.23::ARMCLANG
F (.\main.c)(0x688C3B55)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__EVAL -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./ -I ./STM32F10x_StdPeriph_Driver/inc -I ./CMSIS

-I"D:/Fire BB/ARM/Keli5/Keil/STM32F1xx_DFP/2.4.1/Device/Include"

-D__UVISION_VERSION="542" -DSTM32F10X_MD -DSTM32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./objects/main.o -MMD)
F (.\sys.c)(0x688C3A7D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__EVAL -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./ -I ./STM32F10x_StdPeriph_Driver/inc -I ./CMSIS

-I"D:/Fire BB/ARM/Keli5/Keil/STM32F1xx_DFP/2.4.1/Device/Include"

-D__UVISION_VERSION="542" -DSTM32F10X_MD -DSTM32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./objects/sys.o -MMD)
F (.\delay.c)(0x688C3ABA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__EVAL -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./ -I ./STM32F10x_StdPeriph_Driver/inc -I ./CMSIS

-I"D:/Fire BB/ARM/Keli5/Keil/STM32F1xx_DFP/2.4.1/Device/Include"

-D__UVISION_VERSION="542" -DSTM32F10X_MD -DSTM32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./objects/delay.o -MMD)
F (.\led.c)(0x688C3ACB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__EVAL -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./ -I ./STM32F10x_StdPeriph_Driver/inc -I ./CMSIS

-I"D:/Fire BB/ARM/Keli5/Keil/STM32F1xx_DFP/2.4.1/Device/Include"

-D__UVISION_VERSION="542" -DSTM32F10X_MD -DSTM32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./objects/led.o -MMD)
F (.\key.c)(0x688C3ADB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__EVAL -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./ -I ./STM32F10x_StdPeriph_Driver/inc -I ./CMSIS

-I"D:/Fire BB/ARM/Keli5/Keil/STM32F1xx_DFP/2.4.1/Device/Include"

-D__UVISION_VERSION="542" -DSTM32F10X_MD -DSTM32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./objects/key.o -MMD)
F (.\servo.c)(0x688C3AFC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__EVAL -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./ -I ./STM32F10x_StdPeriph_Driver/inc -I ./CMSIS

-I"D:/Fire BB/ARM/Keli5/Keil/STM32F1xx_DFP/2.4.1/Device/Include"

-D__UVISION_VERSION="542" -DSTM32F10X_MD -DSTM32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./objects/servo.o -MMD)
F (.\stm32f10x_it.c)(0x688C3B42)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__EVAL -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./ -I ./STM32F10x_StdPeriph_Driver/inc -I ./CMSIS

-I"D:/Fire BB/ARM/Keli5/Keil/STM32F1xx_DFP/2.4.1/Device/Include"

-D__UVISION_VERSION="542" -DSTM32F10X_MD -DSTM32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./objects/stm32f10x_it.o -MMD)
