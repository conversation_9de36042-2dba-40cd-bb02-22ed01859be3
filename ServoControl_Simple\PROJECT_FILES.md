# 项目文件清单

## 核心源文件

### main.c
- **功能**：主程序入口，包含系统初始化和主循环
- **主要函数**：
  - `main()`：程序入口
  - `System_Init()`：系统初始化
  - `Servo_Demo()`：舵机演示程序
  - `Servo_SetAngle()`：设置舵机角度

### sys.h / sys.c
- **功能**：系统配置，包含GPIO位带操作宏定义
- **主要功能**：
  - GPIO位带操作宏
  - JTAG/SWD配置
  - 系统寄存器操作

### delay.h / delay.c
- **功能**：延时函数实现
- **主要函数**：
  - `delay_init()`：延时初始化
  - `delay_us()`：微秒延时
  - `delay_ms()`：毫秒延时

### led.h / led.c
- **功能**：LED控制驱动
- **硬件连接**：PA4
- **主要函数**：
  - `LED_Init()`：LED初始化
  - `LED`宏：LED控制

### key.h / key.c
- **功能**：按键输入驱动
- **硬件连接**：PA0（内部上拉）
- **主要函数**：
  - `KEY_Init()`：按键初始化
  - `KEY`宏：按键状态读取

### servo.h / servo.c
- **功能**：舵机PWM控制驱动
- **硬件连接**：PB8(TIM4_CH3), PB9(TIM4_CH4)
- **主要函数**：
  - `Servo_Init()`：舵机PWM初始化
  - `Servo_SetPWM()`：设置PWM值

## 系统配置文件

### stm32f10x_conf.h
- **功能**：STM32标准外设库配置文件
- **作用**：包含所需的外设头文件，配置库参数

### stm32f10x_it.h / stm32f10x_it.c
- **功能**：中断服务程序
- **包含**：系统异常处理函数和外设中断处理函数

## 项目配置文件

### ServoControl.uvprojx
- **功能**：Keil uVision项目配置文件
- **包含**：
  - 编译器设置
  - 链接器配置
  - 调试器设置
  - 文件组织结构

### Makefile
- **功能**：命令行编译配置
- **支持命令**：
  - `make`：编译项目
  - `make clean`：清理编译文件
  - `make flash`：烧录程序

## 文档文件

### README.md
- **功能**：完整的项目说明文档
- **内容**：
  - 项目介绍
  - 硬件连接
  - 软件功能
  - 编译烧录
  - 故障排除

### QUICK_START.md
- **功能**：快速开始指南
- **内容**：
  - 5分钟快速部署
  - 常见问题解决
  - 进阶使用技巧

### PROJECT_FILES.md
- **功能**：项目文件清单（本文件）
- **内容**：所有文件的功能说明

## 文件依赖关系

```
main.c
├── stm32f10x.h
├── stm32f10x_conf.h
├── sys.h
├── delay.h
├── led.h
├── key.h
└── servo.h

sys.c
└── sys.h

delay.c
└── delay.h

led.c
├── led.h
└── sys.h

key.c
├── key.h
└── sys.h

servo.c
└── servo.h

stm32f10x_it.c
└── stm32f10x_it.h
```

## 编译输出文件

### Objects/ 目录
- **ServoControl.axf**：可执行文件
- **ServoControl.hex**：Intel HEX格式文件（用于烧录）
- **ServoControl.map**：内存映射文件
- **\*.o**：目标文件
- **\*.d**：依赖文件

### Listings/ 目录
- **ServoControl.lst**：汇编列表文件
- **\*.lst**：各模块汇编列表

## 使用说明

1. **开发环境**：推荐使用Keil uVision 5.x
2. **必需文件**：所有.c和.h文件都是必需的
3. **可选文件**：Makefile用于命令行编译
4. **文档文件**：README.md等用于说明，不参与编译

## 修改指南

### 添加新功能
1. 创建对应的.h和.c文件
2. 在main.c中包含头文件
3. 在项目配置中添加源文件

### 修改硬件连接
1. 修改对应驱动文件中的引脚定义
2. 更新README.md中的连接说明
3. 重新编译和测试

### 优化代码
1. 可以移除不需要的外设库文件
2. 调整编译优化级别
3. 精简功能以减少代码大小
