# 快速开始指南

## 5分钟快速部署

### 第一步：准备硬件
1. STM32F103C8T6开发板（蓝丸板）
2. 2个SG90舵机
3. 1个LED + 330Ω电阻
4. 1个按键
5. 杜邦线若干
6. 5V电源（用于舵机）

### 第二步：硬件连接
```
舵机1：
- 红线 → 5V
- 棕线 → GND  
- 橙线 → PB8

舵机2：
- 红线 → 5V
- 棕线 → GND
- 橙线 → PB9

LED：
- 正极 → PA4（通过330Ω电阻）
- 负极 → GND

按键：
- 一端 → PA0
- 另一端 → GND
```

### 第三步：软件烧录

#### 使用Keil uVision（推荐）
1. 安装Keil uVision 5
2. 安装STM32F1xx器件支持包
3. 打开`ServoControl.uvprojx`
4. 编译项目（F7）
5. 连接ST-Link
6. 下载程序（F8）

#### 使用STM32CubeProgrammer
1. 下载并安装STM32CubeProgrammer
2. 连接ST-Link到电脑和开发板
3. 打开STM32CubeProgrammer
4. 选择ST-LINK连接
5. 点击"Connect"
6. 选择编译生成的HEX文件
7. 点击"Download"

### 第四步：测试运行
1. 上电后LED会闪烁一次
2. 舵机会转到90度中位
3. 按下按键，LED点亮，进入演示模式
4. 舵机开始自动运动
5. 再次按键，LED熄灭，回到手动模式

## 常见问题快速解决

### 舵机不动
- 检查5V电源是否连接
- 确认信号线连接到正确引脚
- 检查GND是否共地

### 编译失败
- 确认Keil版本支持STM32F1
- 检查器件支持包是否安装
- 确认项目路径无中文字符

### 烧录失败
- 检查ST-Link驱动是否安装
- 确认开发板电源正常
- 尝试按住RESET键再烧录

### 按键无响应
- 检查按键连接
- 确认按键另一端接GND
- 检查PA0引脚连接

## 进阶使用

### 修改舵机角度范围
在`servo.h`中修改：
```c
#define SERVO_MIN_PWM       50      // 最小角度PWM值
#define SERVO_MAX_PWM       250     // 最大角度PWM值
```

### 修改演示动作
在`main.c`的`Servo_Demo()`函数中修改动作序列。

### 添加更多舵机
1. 使用其他定时器通道（如TIM3_CH1/CH2）
2. 修改`servo.c`添加新的初始化函数
3. 在`main.c`中调用新的控制函数

## 技术支持

遇到问题请查看完整的README.md文档，或参考原厂技术资料。
