# STM32F103C8T6 舵机控制简单项目

## 项目简介

这是一个基于STM32F103C8T6微控制器的舵机控制项目，实现了两个舵机的基本控制功能。项目包含演示模式和手动模式，通过按键可以切换模式。

## 硬件连接

### 主要器件
- 主控芯片：STM32F103C8T6
- 舵机：2个标准舵机（SG90或类似）
- LED指示灯：1个
- 按键：1个

### 引脚连接
| 功能 | 引脚 | 说明 |
|------|------|------|
| 舵机1 | PB8 (TIM4_CH3) | PWM输出控制舵机1 |
| 舵机2 | PB9 (TIM4_CH4) | PWM输出控制舵机2 |
| LED | PA4 | 状态指示LED（需串联限流电阻330Ω） |
| 按键 | PA0 | 模式切换按键（低电平有效，内部上拉） |
| 电源 | 3.3V/5V | 根据舵机要求选择 |
| 地线 | GND | 公共地线 |

### 舵机连接说明
- 舵机电源线（红色）：连接到5V电源
- 舵机地线（棕色/黑色）：连接到GND
- 舵机信号线（橙色/黄色）：连接到对应的PWM引脚

### 电路连接示意
```
STM32F103C8T6
    ┌─────────────┐
    │             │
PB8 │●            │ 5V  ●─────── 舵机1电源（红）
PB9 │●            │ GND ●─────── 舵机1地线（棕）
PA4 │●            │     │   ┌─── 舵机1信号（橙）
PA0 │●            │     │   │
GND │●            │     │   │   ┌─── 舵机2电源（红）
3V3 │●            │     │   │   │
    │             │     │   │   │   ┌─── 舵机2地线（棕）
    └─────────────┘     │   │   │   │
         │               │   │   │   │
         │               └───┼───┼───┼─── GND
         │                   │   │   │
    ┌────┴────┐              │   │   └─── 舵机2信号（橙）
    │ LED+330Ω│              │   │
    └─────────┘              │   └─────── 舵机1信号
                             │
    ┌─────────┐              └─────────── 舵机2信号
    │  按键   │
    └─────────┘
```

## 软件功能

### 主要特性
1. **双舵机控制**：同时控制两个舵机
2. **角度控制**：支持0-180度角度控制
3. **演示模式**：自动演示舵机运动
4. **手动模式**：舵机保持在90度中位
5. **按键切换**：通过按键切换工作模式
6. **LED指示**：显示当前工作状态

### 工作模式
- **手动模式**（默认）：两个舵机保持在90度中位
- **演示模式**：舵机按预设程序自动运动

### 操作说明
1. 上电后，LED闪烁一次表示系统初始化完成
2. 默认进入手动模式，舵机保持在90度中位
3. 按下按键切换到演示模式，LED点亮
4. 再次按下按键切换回手动模式，LED熄灭

## 编译和烧录

### 方法一：使用Keil uVision

#### 环境要求
- Keil uVision 5.x
- STM32F1xx器件支持包
- ST-Link调试器

#### 编译步骤
1. 打开Keil uVision
2. 打开项目文件：`ServoControl.uvprojx`
3. 选择目标：`ServoControl`
4. 点击编译按钮或按F7编译项目
5. 编译成功后会生成`ServoControl.hex`文件

#### 烧录步骤
1. 连接ST-Link调试器到STM32开发板
2. 在Keil中点击下载按钮或按F8
3. 等待烧录完成

### 方法二：使用命令行工具

#### 环境要求
- ARM GCC工具链
- STM32标准外设库
- ST-Link工具

#### 编译命令
```bash
# 编译项目
make

# 清理编译文件
make clean
```

#### 烧录命令
```bash
# 使用ST-Link烧录
make flash

# 或者直接使用st-flash命令
st-flash --format ihex write ServoControl.hex
```

### 方法三：使用STM32CubeProgrammer

1. 打开STM32CubeProgrammer
2. 连接到目标设备
3. 选择生成的HEX文件
4. 点击"Download"按钮烧录

## 项目文件结构

```
ServoControl_Simple/
├── main.c              # 主程序文件
├── sys.h/sys.c         # 系统配置
├── delay.h/delay.c     # 延时函数
├── led.h/led.c         # LED控制
├── key.h/key.c         # 按键控制
├── servo.h/servo.c     # 舵机控制
├── stm32f10x_it.h/c    # 中断处理
├── stm32f10x_conf.h    # 标准库配置
├── ServoControl.uvprojx # Keil项目文件
├── Makefile            # Make编译文件
└── README.md           # 说明文档
```

## 技术参数

### PWM参数
- 频率：50Hz（20ms周期）
- 占空比范围：2.5% - 12.5%
- 脉宽范围：0.5ms - 2.5ms
- 角度对应：0° - 180°

### 系统参数
- 主频：72MHz
- 定时器频率：1MHz
- PWM分辨率：1μs

## 故障排除

### 常见问题

1. **舵机不动作**
   - 检查电源连接是否正确
   - 确认PWM信号线连接
   - 检查舵机电源电压（通常需要5V）

2. **编译错误**
   - 确认STM32标准库路径正确
   - 检查头文件包含路径
   - 确认编译器版本兼容

3. **烧录失败**
   - 检查ST-Link连接
   - 确认目标芯片型号
   - 尝试擦除芯片后重新烧录

4. **按键无响应**
   - 检查按键硬件连接
   - 确认上拉电阻配置
   - 检查消抖延时设置

### 调试建议
1. 使用示波器检查PWM输出波形
2. 通过LED状态判断程序运行状态
3. 使用串口输出调试信息（可选扩展）

## 扩展功能

### 可能的改进
1. 添加串口通信控制
2. 增加角度反馈功能
3. 实现更复杂的运动轨迹
4. 添加速度控制功能
5. 支持更多舵机控制

### 硬件扩展
1. 添加电位器进行手动角度调节
2. 增加LCD显示当前角度
3. 添加蓝牙模块实现无线控制
4. 集成传感器实现自动控制

## 许可证

本项目基于轮趣科技的开源代码修改而来，仅供学习和研究使用。

## 联系信息

如有问题或建议，请通过以下方式联系：
- 项目基于：轮趣科技(东莞)有限公司
- 官网：wheeltec.net
- 淘宝店铺：shop114407458.taobao.com
