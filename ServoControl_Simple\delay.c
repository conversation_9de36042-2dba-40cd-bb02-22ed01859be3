/***********************************************
STM32F103C8T6 舵机控制项目
延时函数源文件
***********************************************/
#include "delay.h"

static u8  fac_us = 0;   // us延时倍乘数			   
static u16 fac_ms = 0;   // ms延时倍乘数

/**
 * @brief 延时初始化
 */
void delay_init(void)
{
    SysTick_CLKSourceConfig(SysTick_CLKSource_HCLK_Div8);  // 选择外部时钟  HCLK/8
    fac_us = SystemCoreClock/8000000;  // 为系统时钟的1/8  
    fac_ms = (u16)fac_us * 1000;       // 每个ms需要的systick时钟数   
}								    

/**
 * @brief 延时nus微秒
 * @param nus: 要延时的us数
 * @note nus的值,不要大于798915us
 */
void delay_us(u32 nus)
{		
    u32 temp;	    	 
    SysTick->LOAD = nus * fac_us;          // 时间加载	  		 
    SysTick->VAL = 0x00;                   // 清空计数器
    SysTick->CTRL |= SysTick_CTRL_ENABLE_Msk;  // 开始倒数	  
    do
    {
        temp = SysTick->CTRL;
    }
    while(temp & 0x01 && !(temp & (1<<16)));  // 等待时间到达   
    SysTick->CTRL &= ~SysTick_CTRL_ENABLE_Msk; // 关闭计数器
    SysTick->VAL = 0X00;       // 清空计数器	 
}

/**
 * @brief 延时nms毫秒
 * @param nms: 要延时的ms数
 * @note nms的值,不要大于65535
 */
void delay_ms(u16 nms)
{	 		  	  
    u32 temp;		   
    SysTick->LOAD = (u32)nms * fac_ms;     // 时间加载(SysTick->LOAD为24bit)
    SysTick->VAL = 0x00;                   // 清空计数器
    SysTick->CTRL |= SysTick_CTRL_ENABLE_Msk;  // 开始倒数  
    do
    {
        temp = SysTick->CTRL;
    }
    while(temp & 0x01 && !(temp & (1<<16)));  // 等待时间到达   
    SysTick->CTRL &= ~SysTick_CTRL_ENABLE_Msk; // 关闭计数器
    SysTick->VAL = 0X00;       // 清空计数器	  	    
}
