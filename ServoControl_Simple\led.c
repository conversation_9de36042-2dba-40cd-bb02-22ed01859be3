/***********************************************
STM32F103C8T6 舵机控制项目
LED控制源文件
***********************************************/
#include "led.h"

/**
 * @brief LED初始化函数
 */
void LED_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能GPIOA时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
    
    // 配置PA4为推挽输出
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4;	      
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP; 
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // 初始状态LED熄灭
    LED = 1;
}
