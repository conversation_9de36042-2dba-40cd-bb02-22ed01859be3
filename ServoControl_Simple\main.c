/***********************************************
公司：轮趣科技(东莞)有限公司
品牌：WHEELTEC
官网：wheeltec.net
淘宝店铺：shop114407458.taobao.com 
速卖通: https://minibalance.aliexpress.com/store/4455017
版本：V1.0
修改时间：2025-01-01

STM32F103C8T6 舵机控制简单项目
功能：控制两个舵机进行简单的角度控制
硬件连接：
- 舵机1：PB8 (TIM4_CH3)
- 舵机2：PB9 (TIM4_CH4)
- LED：PA4
- 按键：PA0

All rights reserved
***********************************************/

#include "stm32f10x.h"
#include "stm32f10x_conf.h"
#include "sys.h"
#include "delay.h"
#include "led.h"
#include "key.h"
#include "servo.h"

// 全局变量
float Servo1_Angle = 90.0f;    // 舵机1角度 (0-180度)
float Servo2_Angle = 90.0f;    // 舵机2角度 (0-180度)
uint8_t demo_mode = 0;         // 演示模式标志

// 函数声明
void System_Init(void);
void Servo_Demo(void);
void Servo_SetAngle(uint8_t servo_id, float angle);

int main(void)
{
    // 系统初始化
    System_Init();
    
    // 舵机初始化到中位
    Servo_SetAngle(1, 90.0f);
    Servo_SetAngle(2, 90.0f);
    
    // 启动指示
    LED = 0;  // 点亮LED
    delay_ms(500);
    LED = 1;  // 熄灭LED
    
    while(1)
    {
        // 检测按键
        if(KEY == 0)  // 按键按下
        {
            delay_ms(20);  // 消抖
            if(KEY == 0)
            {
                demo_mode = !demo_mode;  // 切换演示模式
                LED = !LED;  // LED状态指示
                
                // 等待按键释放
                while(KEY == 0);
                delay_ms(20);
            }
        }
        
        if(demo_mode)
        {
            // 演示模式：自动控制舵机
            Servo_Demo();
        }
        else
        {
            // 手动模式：舵机保持在90度中位
            Servo_SetAngle(1, 90.0f);
            Servo_SetAngle(2, 90.0f);
            delay_ms(100);
        }
    }
}

/**
 * @brief 系统初始化
 */
void System_Init(void)
{
    // 延时函数初始化
    delay_init();
    
    // 开启SWD调试接口
    JTAG_Set(SWD_ENABLE);
    
    // 中断优先级分组
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    
    // 硬件初始化
    LED_Init();     // LED初始化
    KEY_Init();     // 按键初始化
    Servo_Init();   // 舵机PWM初始化
    
    // 延时等待系统稳定
    delay_ms(100);
}

/**
 * @brief 舵机演示程序
 */
void Servo_Demo(void)
{
    static uint32_t demo_step = 0;
    static uint32_t delay_count = 0;
    
    delay_count++;
    
    switch(demo_step)
    {
        case 0:  // 舵机1从90度转到0度
            if(delay_count >= 20)  // 100ms间隔
            {
                Servo1_Angle -= 2.0f;
                if(Servo1_Angle <= 0.0f)
                {
                    Servo1_Angle = 0.0f;
                    demo_step = 1;
                }
                Servo_SetAngle(1, Servo1_Angle);
                delay_count = 0;
            }
            break;
            
        case 1:  // 舵机1从0度转到180度
            if(delay_count >= 20)
            {
                Servo1_Angle += 2.0f;
                if(Servo1_Angle >= 180.0f)
                {
                    Servo1_Angle = 180.0f;
                    demo_step = 2;
                }
                Servo_SetAngle(1, Servo1_Angle);
                delay_count = 0;
            }
            break;
            
        case 2:  // 舵机1回到90度，舵机2开始动作
            if(delay_count >= 20)
            {
                Servo1_Angle -= 2.0f;
                if(Servo1_Angle <= 90.0f)
                {
                    Servo1_Angle = 90.0f;
                    demo_step = 3;
                }
                Servo_SetAngle(1, Servo1_Angle);
                delay_count = 0;
            }
            break;
            
        case 3:  // 舵机2从90度转到0度
            if(delay_count >= 20)
            {
                Servo2_Angle -= 2.0f;
                if(Servo2_Angle <= 0.0f)
                {
                    Servo2_Angle = 0.0f;
                    demo_step = 4;
                }
                Servo_SetAngle(2, Servo2_Angle);
                delay_count = 0;
            }
            break;
            
        case 4:  // 舵机2从0度转到180度
            if(delay_count >= 20)
            {
                Servo2_Angle += 2.0f;
                if(Servo2_Angle >= 180.0f)
                {
                    Servo2_Angle = 180.0f;
                    demo_step = 5;
                }
                Servo_SetAngle(2, Servo2_Angle);
                delay_count = 0;
            }
            break;
            
        case 5:  // 舵机2回到90度，重新开始
            if(delay_count >= 20)
            {
                Servo2_Angle -= 2.0f;
                if(Servo2_Angle <= 90.0f)
                {
                    Servo2_Angle = 90.0f;
                    demo_step = 0;
                    delay_ms(1000);  // 暂停1秒后重新开始
                }
                Servo_SetAngle(2, Servo2_Angle);
                delay_count = 0;
            }
            break;
    }
    
    delay_ms(5);  // 基础延时
}

/**
 * @brief 设置舵机角度
 * @param servo_id: 舵机ID (1或2)
 * @param angle: 角度值 (0-180度)
 */
void Servo_SetAngle(uint8_t servo_id, float angle)
{
    uint16_t pwm_value;

    // 限制角度范围
    if(angle < 0.0f) angle = 0.0f;
    if(angle > 180.0f) angle = 180.0f;

    // 角度转换为PWM值
    // 舵机PWM: 0.5ms(0度) - 1.5ms(90度) - 2.5ms(180度)
    // 对应PWM值: 50 - 150 - 250 (基于20ms周期，50Hz频率，1MHz计数频率)
    pwm_value = (uint16_t)(SERVO_MIN_PWM + (angle / 180.0f) * (SERVO_MAX_PWM - SERVO_MIN_PWM));

    // 使用舵机驱动函数设置PWM值
    Servo_SetPWM(servo_id, pwm_value);
}
