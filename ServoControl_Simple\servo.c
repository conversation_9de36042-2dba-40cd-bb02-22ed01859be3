/***********************************************
STM32F103C8T6 舵机控制项目
舵机控制源文件
***********************************************/
#include "servo.h"

/**
 * @brief 舵机PWM初始化
 * @note 使用TIM4的CH3(PB8)和CH4(PB9)输出PWM信号控制舵机
 *       PWM频率: 50Hz (20ms周期)
 *       PWM占空比: 0.5ms-2.5ms对应0-180度
 */
void Servo_Init(void)
{		 			
    GPIO_InitTypeDef GPIO_InitStructure;
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
    TIM_OCInitTypeDef TIM_OCInitStructure;

    // 使能TIM4和GPIOB时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM4, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);
                                                                        	
    // 配置PB8和PB9为复用推挽输出
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8 | GPIO_Pin_9; 
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOB, &GPIO_InitStructure);

    // 配置TIM4时基单元
    TIM_TimeBaseStructure.TIM_Period = SERVO_PWM_PERIOD;        // 自动重装载值 (20ms)
    TIM_TimeBaseStructure.TIM_Prescaler = SERVO_PWM_PRESCALER;  // 预分频器
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;     // 时钟分割
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up; // 向上计数模式
    TIM_TimeBaseInit(TIM4, &TIM_TimeBaseStructure);

    // 配置TIM4 CH3输出比较
    TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;           // PWM模式1
    TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable; // 输出使能
    TIM_OCInitStructure.TIM_Pulse = SERVO_MID_PWM;              // 初始占空比(90度)
    TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High;   // 输出极性高
    TIM_OC3Init(TIM4, &TIM_OCInitStructure);
    
    // 配置TIM4 CH4输出比较
    TIM_OC4Init(TIM4, &TIM_OCInitStructure);

    // 使能预装载寄存器
    TIM_OC3PreloadConfig(TIM4, TIM_OCPreload_Enable);
    TIM_OC4PreloadConfig(TIM4, TIM_OCPreload_Enable);
    
    // 使能自动重装载预装载寄存器
    TIM_ARRPreloadConfig(TIM4, ENABLE);
    
    // 使能TIM4
    TIM_Cmd(TIM4, ENABLE);
    
    // 设置初始PWM值(90度中位)
    TIM4->CCR3 = SERVO_MID_PWM;
    TIM4->CCR4 = SERVO_MID_PWM;
} 

/**
 * @brief 设置舵机PWM值
 * @param servo_id: 舵机ID (1=CH3/PB8, 2=CH4/PB9)
 * @param pwm_value: PWM比较值
 */
void Servo_SetPWM(uint8_t servo_id, uint16_t pwm_value)
{
    // 限制PWM值范围
    if(pwm_value < SERVO_MIN_PWM) pwm_value = SERVO_MIN_PWM;
    if(pwm_value > SERVO_MAX_PWM) pwm_value = SERVO_MAX_PWM;
    
    // 设置对应通道的PWM值
    if(servo_id == 1)
    {
        TIM4->CCR3 = pwm_value;  // PB8 - TIM4_CH3
    }
    else if(servo_id == 2)
    {
        TIM4->CCR4 = pwm_value;  // PB9 - TIM4_CH4
    }
}
