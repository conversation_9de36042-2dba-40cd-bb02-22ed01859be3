/***********************************************
STM32F103C8T6 舵机控制项目
舵机控制头文件
***********************************************/
#ifndef __SERVO_H
#define __SERVO_H

#include "stm32f10x.h"

// 舵机PWM相关定义
#define SERVO_PWM_PERIOD    19999   // PWM周期 (20ms, 50Hz)
#define SERVO_PWM_PRESCALER 71      // 预分频器 (72MHz/72=1MHz)

// 舵机角度对应的PWM值
#define SERVO_MIN_PWM       50      // 0度对应的PWM值 (0.5ms)
#define SERVO_MID_PWM       150     // 90度对应的PWM值 (1.5ms)  
#define SERVO_MAX_PWM       250     // 180度对应的PWM值 (2.5ms)

// 函数声明
void Servo_Init(void);
void Servo_SetPWM(uint8_t servo_id, uint16_t pwm_value);

#endif
