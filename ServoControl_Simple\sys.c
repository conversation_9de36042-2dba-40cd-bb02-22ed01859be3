/***********************************************
STM32F103C8T6 舵机控制项目
系统配置源文件
***********************************************/
#include "sys.h"

/**
 * @brief JTAG模式设置,用于设置JTAG的模式
 * @param mode: JTAG,SWD模式设置
 *   @arg JTAG_SWD_DISABLE: 关闭JTAG和SWD
 *   @arg SWD_ENABLE: 关闭JTAG,开启SWD
 *   @arg JTAG_SWD_ENABLE: 开启JTAG和SWD
 */
void JTAG_Set(u8 mode)
{
    u32 temp;
    temp = mode;
    temp <<= 25;
    RCC->APB2ENR |= 1<<0;     // 开启辅助时钟	   
    AFIO->MAPR &= 0XF8FFFFFF; // 清除MAPR的[26:24]
    AFIO->MAPR |= temp;       // 设置jtag模式
}
